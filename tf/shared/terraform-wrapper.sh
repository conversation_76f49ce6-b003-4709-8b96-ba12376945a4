#!/bin/bash

# Terraform wrapper script for environment-specific deployments
# Usage: ./terraform-wrapper.sh <environment> <terraform-command> [additional-args]
#
# Examples:
#   ./terraform-wrapper.sh production plan
#   ./terraform-wrapper.sh staging apply
#   ./terraform-wrapper.sh development destroy

set -e

if [ $# -lt 2 ]; then
    echo "Usage: $0 <environment> <terraform-command> [additional-args]"
    echo "Available environments: development, staging, production"
    exit 1
fi

ENVIRONMENT=$1
TERRAFORM_CMD=$2
shift 2

# Validate environment
case $ENVIRONMENT in
    development|staging|production)
        ;;
    *)
        echo "Error: Invalid environment '$ENVIRONMENT'"
        echo "Available environments: development, staging, production"
        exit 1
        ;;
esac

# Set paths
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SHARED_DIR="$SCRIPT_DIR"
ENV_DIR="$SCRIPT_DIR/../environments/$ENVIRONMENT"

# Validate environment directory exists
if [ ! -d "$ENV_DIR" ]; then
    echo "Error: Environment directory not found: $ENV_DIR"
    exit 1
fi

# Create temporary working directory
TEMP_DIR=$(mktemp -d)
trap "rm -rf $TEMP_DIR" EXIT

echo "Setting up terraform configuration for environment: $ENVIRONMENT"

# Copy shared configuration files
cp "$SHARED_DIR/main.tf" "$TEMP_DIR/"
cp "$SHARED_DIR/providers.tf" "$TEMP_DIR/"
cp "$SHARED_DIR/variables.tf" "$TEMP_DIR/"
cp "$SHARED_DIR/services.auto.tfvars" "$TEMP_DIR/"

# Copy environment-specific configuration
cp "$ENV_DIR/terraform.tfvars" "$TEMP_DIR/"

# Add backend configuration for the specific environment
cat >> "$TEMP_DIR/main.tf" << EOF

############ BACKEND CONFIGURATION ############
terraform {
  backend "local" {
    path = "$ENV_DIR/terraform-$ENVIRONMENT.tfstate"
  }
}
EOF

# Change to temp directory and run terraform
cd "$TEMP_DIR"

echo "Running: terraform $TERRAFORM_CMD $@"
terraform "$TERRAFORM_CMD" "$@"
